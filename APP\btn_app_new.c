#include "btn_app.h"

extern TIM_HandleTypeDef htim14;
extern uint32_t uwTick;

// 消息队列相关
extern uint8_t msg_up, msg_down, msg_left, msg_right, msg_return, msg_click;

extern uint8_t ucLed[6]; // LED状态数组

typedef enum // 按键枚举定义
{
    USER_BUTTON_0 = 0, // 按键0
    USER_BUTTON_1,     // 按键1(采样启停控制)
    USER_BUTTON_2,     // 按键2(5s周期)
    USER_BUTTON_3,     // 按键3(10s周期)
    USER_BUTTON_4,     // 按键4(15s周期)
    USER_BUTTON_5,     // 按键5
    USER_BUTTON_MAX,   // 按键最大值

    //    USER_BUTTON_COMBO_0 = 0x100, // 组合按键(预留)
    //    USER_BUTTON_COMBO_1,
    //    USER_BUTTON_COMBO_2,
    //    USER_BUTTON_COMBO_3,
    //    USER_BUTTON_COMBO_MAX,
} user_button_t;

// 按键参数配置：消抖时间20ms，长按判断0ms，连击间隔20ms，长按触发1000ms，连击次数0，长按周期1000ms，最大连击10次
static const ebtn_btn_param_t defaul_ebtn_param = EBTN_PARAMS_INIT(20, 0, 20, 1000, 0, 1000, 10);

static ebtn_btn_t btns[] = { // 按键数组初始化
    EBTN_BUTTON_INIT(USER_BUTTON_0, &defaul_ebtn_param), // 按键0
    EBTN_BUTTON_INIT(USER_BUTTON_1, &defaul_ebtn_param), // 按键1
    EBTN_BUTTON_INIT(USER_BUTTON_2, &defaul_ebtn_param), // 按键2
    EBTN_BUTTON_INIT(USER_BUTTON_3, &defaul_ebtn_param), // 按键3
    EBTN_BUTTON_INIT(USER_BUTTON_4, &defaul_ebtn_param), // 按键4
    EBTN_BUTTON_INIT(USER_BUTTON_5, &defaul_ebtn_param), // 按键5
};

uint8_t prv_btn_get_state(struct ebtn_btn *btn) // 获取按键状态 输入:按键结构体指针 输出:按键状态(1=按下,0=松开)
{
    switch (btn->key_id) // 根据按键ID读取GPIO状态
    {
    case USER_BUTTON_0:
        return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_15); // 按键0(PE15)低电平有效
    case USER_BUTTON_1:
        return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_13); // 按键1(PE13)低电平有效
    case USER_BUTTON_2:
        return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_11); // 按键2(PE11)低电平有效
    case USER_BUTTON_3:
        return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_9);  // 按键3(PE9)低电平有效
    case USER_BUTTON_4:
        return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_7);  // 按键4(PE7)低电平有效
    case USER_BUTTON_5:
        return !HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_0);  // 按键5(PB0)低电平有效
    default:
        return 0; // 无效按键返回0
    }
}

void prv_btn_event(struct ebtn_btn *btn, ebtn_evt_t evt) // 按键事件处理函数 输入:按键结构体指针,事件类型 输出:无
{
    if (evt == EBTN_EVT_ONPRESS) // 按键按下事件处理
    {
        switch (btn->key_id) // 根据按键ID执行相应功能
        {
        case USER_BUTTON_0:
            WOUOUI_MSG_QUE_SEND(msg_down);

            // 采样启停控制按键 (KEY1)
            // 确保采样系统已初始化
            sampling_init();

            if (sampling_get_state() == SAMPLING_IDLE)
            {
                // 启动采样系统
                if (sampling_start() == SAMPLING_OK)
                {
                    sampling_cycle_t cycle = sampling_get_cycle();
                    my_printf(&huart1, "Periodic Sampling\r\n");
                    my_printf(&huart1, "sample cycle: %ds\r\n", (int)cycle);

                    // 设置采样输出标志
                    extern uint8_t g_sampling_output_enabled;
                    extern uint32_t g_last_output_time;
                    g_sampling_output_enabled = 1;
                    g_last_output_time = HAL_GetTick();

                    // 记录启动日志
                    char log_msg[64];
                    sprintf(log_msg, "sample start - cycle %ds (key0)", (int)cycle);
                    data_storage_write_log(log_msg);
                }
                else
                {
                    my_printf(&huart1, "sampling start failed.\r\n");
                }
            }
            else
            {
                // 停止采样系统
                if (sampling_stop() == SAMPLING_OK)
                {
                    my_printf(&huart1, "PeriodicSamplingSTOP\r\n");

                    // 设置采样输出标志
                    extern uint8_t g_sampling_output_enabled;
                    g_sampling_output_enabled = 0;

                    // 记录停止日志
                    data_storage_write_log("sample stop (key0)");
                }
                else
                {
                    my_printf(&huart1, "sampling stop failed.\r\n");
                }
            }
            break;
        case USER_BUTTON_1:
            WOUOUI_MSG_QUE_SEND(msg_left);

            // 设置5s采样周期 (KEY2)
            sampling_init();
            if (sampling_set_cycle(CYCLE_5S) == SAMPLING_OK)
            {
                my_printf(&huart1, "sample cycle adjust: 5s\r\n");

                // 记录周期调整日志
                data_storage_write_log("cycle adjust to 5s (key1)");
            }
            else
            {
                my_printf(&huart1, "cycle adjust failed.\r\n");
            }
            break;
        case USER_BUTTON_2:
            WOUOUI_MSG_QUE_SEND(msg_right);

            // 设置10s采样周期 (KEY3)
            sampling_init();
            if (sampling_set_cycle(CYCLE_10S) == SAMPLING_OK)
            {
                my_printf(&huart1, "sample cycle adjust: 10s\r\n");

                // 记录周期调整日志
                data_storage_write_log("cycle adjust to 10s (key2)");
            }
            else
            {
                my_printf(&huart1, "cycle adjust failed.\r\n");
            }
            break;
        case USER_BUTTON_3:
            WOUOUI_MSG_QUE_SEND(msg_return);

            // 设置15s采样周期 (KEY4)
            sampling_init();
            if (sampling_set_cycle(CYCLE_15S) == SAMPLING_OK)
            {
                my_printf(&huart1, "sample cycle adjust: 15s\r\n");

                // 记录周期调整日志
                data_storage_write_log("cycle adjust to 15s (key3)");
            }
            else
            {
                my_printf(&huart1, "cycle adjust failed.\r\n");
            }
            break;
        case USER_BUTTON_4:
            WOUOUI_MSG_QUE_SEND(msg_click);
            break;
        case USER_BUTTON_5:
            WOUOUI_MSG_QUE_SEND(msg_click);
            break;
        default:
            break;
        }
    }
}

void app_btn_init(void)
{
    // ebtn_init(btns, EBTN_ARRAY_SIZE(btns), btns_combo, EBTN_ARRAY_SIZE(btns_combo), prv_btn_get_state, prv_btn_event);
    ebtn_init(btns, EBTN_ARRAY_SIZE(btns), NULL, 0, prv_btn_get_state, prv_btn_event);

    //    ebtn_combo_btn_add_btn(&btns_combo[0], USER_BUTTON_0);
    //    ebtn_combo_btn_add_btn(&btns_combo[0], USER_BUTTON_1);

    //    ebtn_combo_btn_add_btn(&btns_combo[1], USER_BUTTON_2);
    //    ebtn_combo_btn_add_btn(&btns_combo[1], USER_BUTTON_3);

    HAL_TIM_Base_Start_IT(&htim14);
}

void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
}

void btn_task(void)
{
    ebtn_process(uwTick);
}
