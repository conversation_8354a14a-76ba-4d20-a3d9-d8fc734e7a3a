// 文件名：sampling_control.c
// 功能：采样控制系统实现，提供数据采集控制和状态管理功能
// 作者：蓝牙电子工程师
// 版权：Copyright (c) 2024 蓝牙电子工程师. All rights reserved.

#include "sampling_control.h"
#include "stddef.h"
#include "config_manager.h" // 配置管理系统
#include "adc_app.h"        // ADC应用模块

static sampling_control_t g_sampling_control = {0}; // 全局采样控制实例
static uint8_t g_sampling_initialized = 0;          // 初始化标志

sampling_status_t sampling_init(void) // 初始化采样控制系统
{
    if (g_sampling_initialized) // 检查是否已初始化
    {
        return SAMPLING_OK;
    }

    config_init(); // 初始化配置管理系统

    g_sampling_control.state = SAMPLING_IDLE;                       // 设置初始状态为空闲
    g_sampling_control.cycle = config_get_sampling_cycle();         // 从配置系统获取周期
    g_sampling_control.last_sample_time = 0;                        // 清零上次采样时间

    g_sampling_initialized = 1; // 设置初始化标志
    return SAMPLING_OK;
}

sampling_status_t sampling_start(void) // 启动采样
{
    if (!g_sampling_initialized) // 检查初始化状态
        return SAMPLING_ERROR;

    g_sampling_control.state = SAMPLING_ACTIVE;                     // 设置为活动状态
    g_sampling_control.last_sample_time = HAL_GetTick();            // 记录启动时间

    return SAMPLING_OK;
}

sampling_status_t sampling_stop(void) // 停止采样
{
    if (!g_sampling_initialized) // 检查初始化状态
        return SAMPLING_ERROR;

    g_sampling_control.state = SAMPLING_IDLE;       // 设置为空闲状态

    return SAMPLING_OK;
}

sampling_status_t sampling_set_cycle(sampling_cycle_t cycle) // 设置采样周期
{
    if (!g_sampling_initialized) // 检查初始化状态
        return SAMPLING_ERROR;

    if (cycle != CYCLE_5S && cycle != CYCLE_10S && cycle != CYCLE_15S) // 验证周期参数
    {
        return SAMPLING_INVALID;
    }

    g_sampling_control.cycle = cycle; // 更新本地采样周期

    if (config_set_sampling_cycle(cycle) == CONFIG_OK) // 保存到配置管理系统并写入Flash
    {
        config_save_to_flash();
    }

    return SAMPLING_OK;
}

sampling_state_t sampling_get_state(void) // 获取采样状态
{
    if (!g_sampling_initialized) // 检查初始化状态
        return SAMPLING_IDLE;
    return g_sampling_control.state;
}

sampling_cycle_t sampling_get_cycle(void) // 获取采样周期
{
    if (!g_sampling_initialized) // 检查初始化状态
        return CYCLE_5S;
    return g_sampling_control.cycle;
}

uint8_t sampling_should_sample(void) // 检查是否应该采样
{
    if (!g_sampling_initialized || g_sampling_control.state != SAMPLING_ACTIVE) // 检查初始化和状态
    {
        return 0;
    }

    uint32_t current_time = HAL_GetTick();                                       // 获取当前时间
    uint32_t elapsed_time = current_time - g_sampling_control.last_sample_time; // 计算时间差
    uint32_t cycle_ms = g_sampling_control.cycle * 1000;                        // 转换为毫秒

    return (elapsed_time >= cycle_ms) ? 1 : 0; // 判断是否到达采样时间
}

float sampling_get_voltage(void) // 获取当前电压值
{
    extern __IO float voltage;     // 引用adc_app.c的全局电压变量
    config_params_t config_params; // 配置参数结构体

    if (config_get_params(&config_params) != CONFIG_OK) // 获取配置参数
    {
        return voltage; // 如果获取失败时返回原始电压值
    }

    return voltage * config_params.ratio; // 使用ratio参数计算电压值
}

uint8_t sampling_check_overlimit(void) // 检查是否超限
{
    config_params_t config_params; // 配置参数结构体

    if (config_get_params(&config_params) != CONFIG_OK) // 获取配置参数
    {
        return 0; // 如果获取失败时认为未超限
    }

    float current_voltage = sampling_get_voltage(); // 获取当前电压值

    return (current_voltage > config_params.limit) ? 1 : 0; // 检查是否超过limit阈值
}

void sampling_task(void) // 采样任务函数
{
    if (!g_sampling_initialized) // 检查初始化状态
        return;

    if (g_sampling_control.state == SAMPLING_ACTIVE) // 如果处于采样状态，检查是否需要进行采样
    {
        if (sampling_should_sample()) // 检查是否到达采样时间
        {
            g_sampling_control.last_sample_time = HAL_GetTick(); // 更新采样时间戳

            float current_voltage = sampling_get_voltage();      // 获取当前电压值
            uint8_t is_overlimit = sampling_check_overlimit();   // 检查是否超限

            (void)current_voltage; // 避免未使用变量警告
            (void)is_overlimit;    // 避免未使用变量警告
        }
    }
}
