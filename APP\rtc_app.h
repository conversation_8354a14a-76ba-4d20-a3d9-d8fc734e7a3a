#ifndef __RTC_APP_H__
#define __RTC_APP_H__

#include "mydefine.h"

// ����RTC����
void rtc_proc(void); // ԭ�е�RTC��������

// ʱ�����úͲ�ѯ����
HAL_StatusTypeDef rtc_set_time_from_string(const char *time_str); // ���ַ�������ʱ��
void rtc_print_current_time(void);                                // ��ӡ��ǰʱ��
void rtc_print_time_without_prefix(void);                         // ��ӡ��ǰʱ�䣨������ã�

// ��չ���ܣ�Ϊ��������Ԥ����
void rtc_get_time_info(RTC_TimeTypeDef *current_time, RTC_DateTypeDef *current_date);                                  // ��ȡʱ����Ϣ
void format_time_output(const RTC_TimeTypeDef *sTime, const RTC_DateTypeDef *sDate, char *buffer, size_t buffer_size); // ��ʽ��ʱ�����

#endif
